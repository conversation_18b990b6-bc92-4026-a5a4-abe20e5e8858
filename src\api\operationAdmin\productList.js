import request from '@/utils/request'
// 获取退货报告数据
export function shopList() {
  return request({
    url: '/api/shopinfo/list',
    method: 'get'
  })
}
export function getProductList(params) {
  return request({
    url: 'api/product-performance/weekly-review',
    method: 'get',
    params
  })
}

// 获取产品绩效周度回顾详情
export function getWeeklyReviewDetail(params) {
  return request({
    url: '/api/product-performance/weekly-review-detail',
    method: 'get',
    params
  })
}

// 导出产品绩效周度回顾数据
export function exportWeeklyReview(params) {
  return request({
    url: '/api/product-performance/export-weekly-review',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function editWeeklyReview(data) {
  return request({
    url: '/api/product-performance-target/update-by-asin-and-date-range',
    method: 'put',
    data
  })
}
export default { shopList, getProductList, getWeeklyReviewDetail, exportWeeklyReview, editWeeklyReview }
