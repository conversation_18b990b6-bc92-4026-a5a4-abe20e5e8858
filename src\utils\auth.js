import Cookies from 'js-cookie'
import Config from '@/settings'

const TokenKey = Config.TokenKey
const SupersetTokenKey = 'SUPERSET-TOKEN'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token, rememberMe) {
  if (rememberMe) {
    return Cookies.set(Token<PERSON><PERSON>, token, { expires: Config.tokenCookieExpires })
  } else return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getSupersetToken() {
  return Cookies.get(SupersetTokenKey)
}

export function setSupersetToken(token, rememberMe) {
  if (rememberMe) {
    return Cookies.set(SupersetTokenKey, token, { expires: Config.tokenCookieExpires })
  } else return Cookies.set(SupersetTokenKey, token)
}

export function removeSupersetToken() {
  return Cookies.remove(SupersetTokenKey)
}
