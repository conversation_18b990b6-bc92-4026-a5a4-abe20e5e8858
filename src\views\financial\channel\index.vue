<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!-- CRUD操作 -->
      <div class="crud-opts">
        <span class="crud-opts-left">
          <el-button
            v-permission="permission.upload"
            class="filter-item"
            size="mini"
            type="success"
            icon="el-icon-upload"
            @click="openUploadDialog"
          >
            批量导入
          </el-button>
        </span>
      </div>
    </div>
    <!--表单组件-->

    <!--嵌入的iframe-->
    <div class="iframe-container" :style="{ height: iframeHeight }">
      <iframe
        :src="supersetUrl"
        width="100%"
        height="100%"
        frameborder="0"
        scrolling="auto"
        sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
        referrerpolicy="no-referrer-when-downgrade"
      />
    </div>

    <!--批量导入对话框-->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="uploadDialog"
      title="批量导入"
      width="500px"
      @close="closeUploadDialog"
    >
      <el-upload
        ref="upload"
        :action="uploadApi"
        :headers="uploadHeaders"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        :auto-upload="false"
        multiple
        drag
        accept=".xlsx,.xls,.csv"
        :file-list="fileList"
        name="files"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">支持上传多个Excel文件(.xlsx, .xls, .csv)</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeUploadDialog">取消</el-button>
        <el-button type="primary" :loading="uploadLoading" @click="submitUpload">确认上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import channel from '@/api/financial/channel'
import request from '@/utils/request'
import { getSupersetToken } from '@/api/login'
const defaultForm = { id: null, month: null, createdTime: null, creator: null, status: '处理中', remark: null }
export default {
  name: 'Shop',
  dicts: ['country_status'],
  data() {
    return {
      // iframe高度
      iframeHeight: 'calc(100vh - 200px)',
      // 表格数据
      data: {
        list: []
      },
      // 选择项
      selections: [],
      // 待查询的对象
      query: {
        dateRange: [],
        month: '',
        creator: ''
      },
      // 查询数据的参数
      params: {},
      // 排序规则，默认 id 降序
      sort: ['id,desc'],
      // 页码
      page: {
        page: 1,
        size: 10,
        total: 0
      },
      // 整体loading
      loading: false,
      // 删除的 Loading
      delAllLoading: false,
      // 表单
      form: {},
      // 重置表单
      defaultForm,
      // 状态
      status: {
        cu: 0,
        title: ''
      },
      // 可操作按钮
      optShow: {
        add: true,
        edit: false,
        del: true,
        download: false,
        reset: true
      },
      // 搜索状态
      searchToggle: true,
      // 数据状态
      dataStatus: {},
      // 权限
      permission: {
        add: ['admin', 'serverDeploy:add'],
        edit: ['admin', 'serverDeploy:edit'],
        del: ['admin', 'serverDeploy:del'],
        upload: ['admin', 'serverDeploy:upload']
      },
      // 上传相关
      uploadDialog: false,
      uploadLoading: false,
      uploadApi: process.env.VUE_APP_BASE_API + '/api/excel/batch-upload',
      fileList: [],
      // 提示信息
      msg: {
        submit: '提交成功',
        add: '新增成功',
        edit: '编辑成功',
        del: '删除成功'
      },
      // Superset仪表板基础URL
      supersetBaseUrl: 'http://39.108.82.102:8080/superset/dashboard/p/2rVGmkb1ywq/?standalone=2&show_filters=1&expand_filters=1'
    }
  },
  computed: {
    // 上传请求头
    uploadHeaders() {
      return {
        'Authorization': this.$store.getters.token
      }
    },
    // 动态生成带有username的Superset URL
    supersetUrl() {
      const username = this.$store.getters.username
      console.log('username', username)
      if (username) {
        // 使用&作为参数分隔符，因为baseUrl已经包含查询参数
        return `${this.supersetBaseUrl}&username=${username}`
      }
      console.error('Username is not available', this.supersetBaseUrl)
      return this.supersetBaseUrl
    }
  },
  created() {
    this.init()
    // 确保获取supersetToken
    this.ensureSupersetToken()
  },
  mounted() {
    // 计算初始iframe高度
    this.calculateIframeHeight()
    // 监听窗口大小变化
    window.addEventListener('resize', this.calculateIframeHeight)
  },
  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.calculateIframeHeight)
  },
  methods: {
    ensureSupersetToken() {
      getSupersetToken().then(res => {
        if (res) {
          // 更新store中的supersetToken
          this.$store.commit('SET_SUPERSET_TOKEN', res)
          console.log('SupersetToken获取成功:', res)
        } else {
          console.error('获取SupersetToken失败: 响应中没有supersetToken')
        }
      }).catch(error => {
        console.error('调用getSupersetToken接口失败:', error)
      })
    },
    // 计算iframe高度
    calculateIframeHeight() {
      // 计算可用高度：窗口高度 - 导航栏高度 - 工具栏高度 - 边距等
      // 84 = navbar + tags-view, 100 = 工具栏 + 边距 + padding
      const availableHeight = document.documentElement.clientHeight - 184
      this.iframeHeight = Math.max(availableHeight, 600) + 'px' // 最小高度600px
    },
    // 初始化
    init() {
      this.loading = true
      // 请求数据
      request({
        url: '/api/channel-order-tasks',
        method: 'get',
        params: this.getQueryParams()
      }).then(res => {
        // 处理特定的API响应格式
        if (res.code === 200 && res.data) {
          // 使用API返回的特定数据结构
          this.data.list = res.data.list || []
          this.page.total = res.data.total || 0
          this.page.page = res.data.pageNum || 1
          this.page.size = res.data.pageSize || 10
        } else {
          this.data.list = []
          this.page.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 获取查询参数
    getQueryParams() {
      // 清除参数无值的情况
      Object.keys(this.query).length !== 0 && Object.keys(this.query).forEach(item => {
        if (this.query[item] === null || this.query[item] === '') this.query[item] = undefined
      })
      Object.keys(this.params).length !== 0 && Object.keys(this.params).forEach(item => {
        if (this.params[item] === null || this.params[item] === '') this.params[item] = undefined
      })

      // 处理日期范围
      const params = {
        page: this.page.page - 1,
        size: this.page.size,
        sort: this.sort,
        ...this.params
      }

      // 复制查询参数，但排除dateRange
      const { dateRange, ...queryWithoutDateRange } = this.query
      Object.assign(params, queryWithoutDateRange)

      // 如果有日期范围，添加开始和结束日期参数
      if (dateRange && dateRange.length === 2) {
        params.startTime = dateRange[0]
        params.endTime = dateRange[1]
      }

      return params
    },
    // 当前页改变
    pageChangeHandler(e) {
      this.page.page = e
      this.refresh()
    },
    // 每页条数改变
    sizeChangeHandler(e) {
      this.page.size = e
      this.page.page = 1
      this.refresh()
    },
    // 刷新
    refresh() {
      this.init()
    },
    // 搜索
    toQuery() {
      this.page.page = 1
      this.refresh()
    },
    // 重置查询条件
    resetQuery() {
      this.query = {
        dateRange: [],
        month: '',
        creator: ''
      }
      this.toQuery()
    },
    // 切换搜索显示
    toggleSearch() {
      this.searchToggle = !this.searchToggle
    },
    // 选择改变
    selectionChangeHandler(val) {
      this.selections = val
    },
    // 获取数据状态
    getDataStatus(id) {
      const dataStatus = this.dataStatus[id]
      return dataStatus || { delete: 0, edit: 0 }
    },
    // 检查权限
    checkPer(permissions) {
      return this.$store.getters.roles.some(role => {
        return permissions.includes(role)
      })
    },
    // 通用提示
    submitSuccessNotify() {
      this.$notify({
        title: this.msg.submit,
        type: 'success',
        duration: 2500
      })
    },
    addSuccessNotify() {
      this.$notify({
        title: this.msg.add,
        type: 'success',
        duration: 2500
      })
    },
    editSuccessNotify() {
      this.$notify({
        title: this.msg.edit,
        type: 'success',
        duration: 2500
      })
    },
    delSuccessNotify() {
      this.$notify({
        title: this.msg.del,
        type: 'success',
        duration: 2500
      })
    },
    // 启动添加
    toAdd() {
      this.resetForm()
      this.status.add = 1
      this.status.edit = 0
      this.status.cu = 1
      this.status.title = '添加汇率'
    },
    // 启动编辑
    toEdit(data) {
      this.resetForm(JSON.parse(JSON.stringify(data)))
      this.status.edit = 1
      this.status.add = 0
      this.status.cu = 1
      this.status.title = '编辑汇率'
      this.getDataStatus(this.getDataId(data)).edit = 1
    },
    // 取消编辑/添加
    cancelCU() {
      this.status.cu = 0
    },
    // 提交
    submitCU() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return
        }
        if (this.status.add === 1) {
          this.doAdd()
        } else if (this.status.edit === 1) {
          this.doEdit()
        }
      })
    },
    // 执行添加
    doAdd() {
      this.status.cu = 2
      channel.add(this.form).then(() => {
        this.status.cu = 0
        this.resetForm()
        this.addSuccessNotify()
        this.toQuery()
      }).catch(() => {
        this.status.cu = 1
      })
    },
    // 执行编辑
    doEdit() {
      this.status.cu = 2
      channel.edit(this.form).then(() => {
        this.status.cu = 0
        this.getDataStatus(this.getDataId(this.form)).edit = 0
        this.editSuccessNotify()
        this.resetForm()
        this.refresh()
      }).catch(() => {
        this.status.cu = 1
      })
    },
    // 获取ID
    getDataId(data) {
      return data.id
    },
    // 删除
    del(data) {
      this.$confirm('确定删除该条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.delLoading = true
        const ids = [this.getDataId(data)]
        channel.del(ids).then(() => {
          this.delLoading = false
          this.delSuccessNotify()
          this.dleChangePage(1)
          this.refresh()
        }).catch(() => {
          this.delLoading = false
        })
      }).catch(() => { })
    },
    // 批量删除
    toDelete(data) {
      this.$confirm('确定删除选中的数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.delAllLoading = true
        const ids = []
        data.forEach(val => {
          ids.push(this.getDataId(val))
        })
        channel.del(ids).then(() => {
          this.delAllLoading = false
          this.delSuccessNotify()
          this.dleChangePage(ids.length)
          this.refresh()
        }).catch(() => {
          this.delAllLoading = false
        })
      }).catch(() => { })
    },
    // 预防删除第二页最后一条数据时，或者多选删除第二页的数据时，页码错误导致请求无数据
    dleChangePage(size) {
      if (size === undefined) {
        size = 1
      }
      if (this.data.list.length === size && this.page.page !== 1) {
        this.page.page = this.page.page - 1
      }
    },
    // 重置表单
    resetForm(data) {
      const form = data || this.defaultForm
      this.form = JSON.parse(JSON.stringify(form))
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },
    // 详情
    details(data) {
      // 打开详情页面或弹窗
      this.$notify({
        title: '查看详情',
        message: `查看ID为${data.id}的详情信息`,
        type: 'info',
        duration: 2500
      })
    },
    // 导出
    exportDown(data) {
      this.loading = true
      // 调用导出API
      request({
        url: `/api/channel-order-tasks/export/${data.id}`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        // 创建下载链接
        const blob = new Blob([res])
        const link = document.createElement('a')
        const fileName = `汇率-${data.month || '未知'}.xlsx`
        link.href = URL.createObjectURL(blob)
        link.download = fileName
        link.click()
        URL.revokeObjectURL(link.href)
        this.loading = false
        this.$notify({
          title: '导出成功',
          type: 'success',
          duration: 2500
        })
      }).catch(() => {
        this.loading = false
        this.$notify({
          title: '导出失败',
          type: 'error',
          duration: 2500
        })
      })
    },
    // 批量导入
    openUploadDialog() {
      this.uploadDialog = true
    },
    // 关闭上传对话框
    closeUploadDialog() {
      this.uploadDialog = false
      this.uploadLoading = false
      this.fileList = []
      // 清空文件列表
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
    },
    // 上传前验证
    beforeUpload(file) {
      const isExcel = /\.(xlsx|xls|csv)$/i.test(file.name)
      // const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        this.$message.error('只能上传Excel文件(.xlsx, .xls, .csv)!')
        return false
      }
      // if (!isLt10M) {
      //   this.$message.error('上传文件大小不能超过 10MB!')
      //   return false
      // }
      return true
    },
    // 提交上传 - 参考项目中成功的实现方式
    submitUpload() {
      const fileList = this.$refs.upload.uploadFiles
      if (!fileList || fileList.length === 0) {
        this.$message.warning('请选择要上传的文件!')
        return
      }

      this.uploadLoading = true

      // 使用 Element UI 的内置上传机制
      this.$refs.upload.submit()
    },
    // 上传成功回调
    handleUploadSuccess() {
      this.uploadLoading = false
      this.$notify({
        title: '批量导入成功',
        type: 'success',
        duration: 2500
      })
      this.closeUploadDialog()
      this.refresh() // 刷新数据
    },
    // 上传失败回调
    handleUploadError(error) {
      this.uploadLoading = false
      this.$notify({
        title: '上传失败',
        message: error.message || '文件上传失败',
        type: 'error',
        duration: 2500
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.crud-opts {
  padding: 4px 0;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}

.crud-opts .crud-opts-left {
  position: relative;
  -webkit-flex: 1;
  flex: 1;
}

.crud-opts .crud-opts-right {
  margin-right: 0;
}

.crud-opts .crud-opts-right-button {
  float: right;
}

.iframe-container {
  margin: 20px 0;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  /* 高度通过JavaScript动态设置 */
}

.iframe-container iframe {
  display: block;
  margin: 0 auto;
  border: none;
}
</style>
