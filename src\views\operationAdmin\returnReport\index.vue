<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div class="crud-opts">
        <span class="crud-opts-left">
          <el-button class="filter-item" size="mini" type="success" icon="el-icon-upload" @click="uploadExcel">导入产品EXCEL表格</el-button>
          <el-button class="filter-item" size="mini" type="success" icon="el-icon-upload" @click="uploadTemplate">导入差评问题分类模板</el-button>
          <!-- <el-button class="filter-item" size="mini" type="primary" icon="el-icon-download" @click="toDownload">导出报表</el-button> -->
        </span>
      </div>
    </div>
    <!--嵌入的iframe-->
    <div class="iframe-container" :style="{ height: iframeHeight }">
      <iframe
        :src="supersetUrl"
        width="100%"
        height="100%"
        frameborder="0"
        scrolling="auto"
        sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
        referrerpolicy="no-referrer-when-downgrade"
      />
    </div>
    <!--导入产品EXCEL表格对话框-->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="uploadExcelDialog"
      title="导入产品EXCEL表格"
      width="500px"
      @close="closeUploadExcelDialog"
    >
      <el-upload
        ref="uploadExcel"
        :action="uploadExcelApi"
        :headers="uploadHeaders"
        :on-success="handleUploadExcelSuccess"
        :on-error="handleUploadExcelError"
        :before-upload="beforeUploadExcel"
        :auto-upload="false"
        multiple
        drag
        accept=".xlsx,.xls,.csv"
        :file-list="excelFileList"
        name="files"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">支持上传多个Excel文件(.xlsx, .xls, .csv)</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeUploadExcelDialog">取消</el-button>
        <el-button :loading="uploadExcelLoading" type="primary" @click="submitUploadExcel">确认</el-button>
      </div>
    </el-dialog>

    <!--导入差评问题分类模板对话框-->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="uploadTemplateDialog"
      title="导入差评问题分类模板"
      width="500px"
      @close="closeUploadTemplateDialog"
    >
      <el-upload
        ref="uploadTemplate"
        :action="uploadTemplateApi"
        :headers="uploadHeaders"
        :on-success="handleUploadTemplateSuccess"
        :on-error="handleUploadTemplateError"
        :before-upload="beforeUploadTemplate"
        :auto-upload="false"
        multiple
        drag
        accept=".xlsx,.xls,.csv"
        :file-list="templateFileList"
        name="files"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">支持上传多个Excel文件(.xlsx, .xls, .csv)</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeUploadTemplateDialog">取消</el-button>
        <el-button :loading="uploadTemplateLoading" type="primary" @click="submitUploadTemplate">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getReturnReportData, del, exportReturnReport, getProductCategories, getExcelFieldMapping } from '@/api/operationAdmin/returnReport'
import { downloadFile } from '@/utils'
import { getSupersetToken } from '@/api/login'

export default {
  name: 'ReturnReport',
  components: {},
  data() {
    return {
      supersetBaseUrl: 'http://*************:8080/superset/dashboard/p/2rVGmnQvywq/?standalone=2&show_filters=1&expand_filters=1',
      iframeHeight: 'calc(100vh - 200px)',
      loading: false,
      searchToggle: true,
      data: {
        list: []
      },
      page: {
        page: 1,
        size: 10,
        total: 0
      },
      query: {
        dateRange: this.getCurrentMonthRange(),
        asin: ''
      },
      productCategories: [],
      selections: [],
      permission: {
        add: ['admin', 'returnReport:add'],
        edit: ['admin', 'returnReport:edit'],
        del: ['admin', 'returnReport:del']
      },
      // 导入产品EXCEL表格相关
      uploadExcelDialog: false,
      uploadExcelLoading: false,
      uploadExcelApi: process.env.VUE_APP_BASE_API + '/api/lingxing/products/import',
      excelFileList: [],
      // 导入差评问题分类模板相关
      uploadTemplateDialog: false,
      uploadTemplateLoading: false,
      uploadTemplateApi: process.env.VUE_APP_BASE_API + '/api/lingxing/reviews/negative/classification-template',
      templateFileList: []
    }
  },
  computed: {
    // 上传请求头
    uploadHeaders() {
      return {
        'Authorization': this.$store.getters.token
      }
    },
    // 动态生成带有username的Superset URL
    supersetUrl() {
      const username = this.$store.getters.username
      console.log('username', username)
      if (username) {
        // 使用&作为参数分隔符，因为baseUrl已经包含查询参数
        return `${this.supersetBaseUrl}&username=${username}`
      }
      console.error('Username is not available', this.supersetBaseUrl)
      return this.supersetBaseUrl
    }
  },
  created() {
    this.init()
    this.loadProductCategories()
    this.ensureSupersetToken()
  },
  mounted() {
    // 计算初始iframe高度
    this.calculateIframeHeight()
    // 监听窗口大小变化
    window.addEventListener('resize', this.calculateIframeHeight)
  },
  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.calculateIframeHeight)
  },
  methods: {
    ensureSupersetToken() {
      getSupersetToken().then(res => {
        if (res) {
          this.$store.commit('SET_SUPERSET_TOKEN', res)
          console.log('SupersetToken获取成功:', res)
        } else {
          console.error('获取SupersetToken失败: 响应中没有supersetToken')
        }
      }).catch(error => {
        console.error('调用getSupersetToken接口失败:', error)
      })
    },
    // 计算iframe高度
    calculateIframeHeight() {
      // 计算可用高度：窗口高度 - 导航栏高度 - 工具栏高度 - 边距等
      // 84 = navbar + tags-view, 100 = 工具栏 + 边距 + padding
      const availableHeight = document.documentElement.clientHeight - 184
      this.iframeHeight = Math.max(availableHeight, 600) + 'px' // 最小高度600px
    },
    // 获取当前月的日期范围
    getCurrentMonthRange() {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth()

      // 当前月第一天
      const firstDay = new Date(year, month, 1)
      const startTime = this.formatDateTime(firstDay, '00:00:00')

      // 当前月最后一天
      const lastDay = new Date(year, month + 1, 0)
      const endTime = this.formatDateTime(lastDay, '23:59:59')

      return [startTime, endTime]
    },
    // 格式化日期时间
    formatDateTime(date, time) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day} ${time}`
    },
    // 初始化数据
    init() {
      this.loading = true
      getReturnReportData(this.getQueryParams()).then(res => {
        if (res.success === true) {
          this.data.list = res.list || []
          this.page.total = res.total || 0
          this.page.page = res.page || 1
          this.page.size = res.size || 10
        } else {
          this.data.list = []
          this.page.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
      getExcelFieldMapping().then(res => {
        console.log(res, '222222')
      }).catch(() => {
        this.excelFieldMapping = {}
      })
    },
    // 获取查询参数
    getQueryParams() {
      // 清除参数无值的情况
      Object.keys(this.query).length !== 0 && Object.keys(this.query).forEach(item => {
        if (this.query[item] === null || this.query[item] === '') this.query[item] = undefined
      })

      const params = {
        page: this.page.page,
        size: this.page.size
      }

      // 复制查询参数，但排除dateRange
      const { dateRange, ...queryWithoutDateRange } = this.query
      Object.assign(params, queryWithoutDateRange)

      // 处理日期范围
      if (dateRange && dateRange.length === 2) {
        params.startTime = dateRange[0]
        params.endTime = dateRange[1]
      }

      return params
    },
    // 当前页改变
    pageChangeHandler(e) {
      this.page.page = e
      this.refresh()
    },
    // 每页条数改变
    sizeChangeHandler(e) {
      this.page.size = e
      this.page.page = 1
      this.refresh()
    },
    // 刷新
    refresh() {
      this.init()
    },
    // 搜索
    toQuery() {
      this.page.page = 1
      this.refresh()
    },
    // 日期范围改变时的处理
    onDateRangeChange() {
      // 清空之前选择的产品分类
      this.query.asin = ''
      // 重新加载产品分类
      this.loadProductCategories()
      // 执行搜索
      this.toQuery()
    },
    // 重置查询条件
    resetQuery() {
      this.query = {
        dateRange: this.getCurrentMonthRange(),
        asin: ''
      }
      // 重新加载产品分类
      this.loadProductCategories()
      this.toQuery()
    },
    // 加载产品分类
    loadProductCategories() {
      // 构建请求参数，包含日期范围
      const params = {}
      if (this.query.dateRange && this.query.dateRange.length === 2) {
        params.startDate = this.query.dateRange[0].split(' ')[0] // 提取日期部分，格式：yyyy-MM-dd
        params.endDate = this.query.dateRange[1].split(' ')[0] // 提取日期部分，格式：yyyy-MM-dd
      }

      getProductCategories(params).then(res => {
        console.log(res.data, 'jijiij')
        if (res && res.data) {
          this.productCategories = res.data
        }
      }).catch(error => {
        console.error('获取产品分类失败:', error)
        this.productCategories = []
      })
    },
    // 切换搜索显示
    toggleSearch() {
      this.searchToggle = !this.searchToggle
    },
    // 选择改变
    selectionChangeHandler(val) {
      this.selections = val
    },
    // 新增
    toAdd() {
      // 新增逻辑
      console.log('新增')
    },
    // 删除
    toDelete() {
      if (this.selections.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      this.$confirm('确认删除选中的数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selections.map(item => item.id)
        del(ids).then(() => {
          this.$message.success('删除成功')
          this.refresh()
        })
      })
    },
    // 检查权限
    checkPer(permissions) {
      return this.$store.getters.roles.some(role => {
        return permissions.includes(role)
      })
    },
    // 导出报表
    toDownload() {
      this.loading = true
      // 获取当前查询参数用于导出
      const params = this.getQueryParams()

      exportReturnReport(params).then(res => {
        // 创建下载链接并下载文件
        downloadFile(res, '退货报表', 'xlsx')
        this.loading = false
        this.$notify({
          title: '导出成功',
          type: 'success',
          duration: 2500
        })
      }).catch(() => {
        this.loading = false
        this.$notify({
          title: '导出失败',
          type: 'error',
          duration: 2500
        })
      })
    },
    // 导入产品EXCEL表格
    uploadExcel() {
      this.uploadExcelDialog = true
    },
    // 导入差评问题分类模板
    uploadTemplate() {
      this.uploadTemplateDialog = true
    },
    // 关闭导入产品EXCEL表格对话框
    closeUploadExcelDialog() {
      this.uploadExcelDialog = false
      this.uploadExcelLoading = false
      this.excelFileList = []
      // 清空文件列表
      if (this.$refs.uploadExcel) {
        this.$refs.uploadExcel.clearFiles()
      }
    },
    // 关闭导入差评问题分类模板对话框
    closeUploadTemplateDialog() {
      this.uploadTemplateDialog = false
      this.uploadTemplateLoading = false
      this.templateFileList = []
      // 清空文件列表
      if (this.$refs.uploadTemplate) {
        this.$refs.uploadTemplate.clearFiles()
      }
    },
    // 导入产品EXCEL表格前验证
    beforeUploadExcel(file) {
      const isExcel = /\.(xlsx|xls|csv)$/i.test(file.name)
      if (!isExcel) {
        this.$message.error('只能上传Excel文件(.xlsx, .xls, .csv)!')
        return false
      }
      return true
    },
    // 导入差评问题分类模板前验证
    beforeUploadTemplate(file) {
      const isExcel = /\.(xlsx|xls|csv)$/i.test(file.name)
      if (!isExcel) {
        this.$message.error('只能上传Excel文件(.xlsx, .xls, .csv)!')
        return false
      }
      return true
    },
    // 提交导入产品EXCEL表格
    submitUploadExcel() {
      const fileList = this.$refs.uploadExcel.uploadFiles
      if (!fileList || fileList.length === 0) {
        this.$message.warning('请选择要上传的文件!')
        return
      }
      this.uploadExcelLoading = true
      // 使用 Element UI 的内置上传机制
      this.$refs.uploadExcel.submit()
    },
    // 提交导入差评问题分类模板
    submitUploadTemplate() {
      const fileList = this.$refs.uploadTemplate.uploadFiles
      if (!fileList || fileList.length === 0) {
        this.$message.warning('请选择要上传的文件!')
        return
      }
      this.uploadTemplateLoading = true
      // 使用 Element UI 的内置上传机制
      this.$refs.uploadTemplate.submit()
    },
    // 导入产品EXCEL表格成功回调
    handleUploadExcelSuccess() {
      this.uploadExcelLoading = false
      this.$notify({
        title: '导入产品EXCEL表格成功',
        type: 'success',
        duration: 2500
      })
      this.closeUploadExcelDialog()
      this.refresh() // 刷新数据
    },
    // 导入产品EXCEL表格失败回调
    handleUploadExcelError(error) {
      this.uploadExcelLoading = false
      this.$notify({
        title: '导入产品EXCEL表格失败',
        message: error.message || '文件上传失败',
        type: 'error',
        duration: 2500
      })
    },
    // 导入差评问题分类模板成功回调
    handleUploadTemplateSuccess() {
      this.uploadTemplateLoading = false
      this.$notify({
        title: '导入差评问题分类模板成功',
        type: 'success',
        duration: 2500
      })
      this.closeUploadTemplateDialog()
      this.refresh() // 刷新数据
    },
    // 导入差评问题分类模板失败回调
    handleUploadTemplateError(error) {
      this.uploadTemplateLoading = false
      this.$notify({
        title: '导入差评问题分类模板失败',
        message: error.message || '文件上传失败',
        type: 'error',
        duration: 2500
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
.iframe-container {
  margin: 20px 0;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  /* 高度通过JavaScript动态设置 */
}

.iframe-container iframe {
  display: block;
  margin: 0 auto;
  border: none;
}
</style>
