import request from '@/utils/request'

// 获取退货报告数据
export function getReturnReportData(params) {
  return request({
    url: 'api/lingxing/refund-orders/by-asin',
    method: 'get',
    params
  })
}

// 导出退货报告数据
export function exportNegativeReview(params) {
  return request({
    url: 'api/lingxing/reviews-report',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export default { getReturnReportData, exportNegativeReview }
