import request from '@/utils/request'

export function login(username, password, code, uuid) {
  return request({
    url: 'auth/login',
    method: 'post',
    data: {
      username,
      password,
      code,
      uuid
    }
  })
}

export function getInfo() {
  return request({
    url: 'auth/info',
    method: 'get'
  })
}

// Verification code API removed
// export function getCodeImg() {
//   return request({
//     url: 'auth/code',
//     method: 'get'
//   })
// }

export function logout() {
  return request({
    url: 'auth/logout',
    method: 'delete'
  })
}
/* 获取SupersetToken信息*/
export function getSupersetToken() {
  return request({
    url: 'auth/querySuperSetToken',
    method: 'get'
  })
}
