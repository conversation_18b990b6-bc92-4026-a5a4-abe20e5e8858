<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable placeholder="输入店铺名称搜索" style="width: 200px" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation />
      </div>
      <crudOperation :permission="permission" />
    </div>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="470px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="85px">
        <el-form-item label="店铺名称" prop="name">
          <el-input v-model="form.name" style="width: 300px" />
        </el-form-item>
        <el-form-item label="站点" prop="location">
          <el-select v-model="form.location" clearable placeholder="请选择" class="filter-item" style="width: 300px">
            <el-option v-for="item in dict.site_status" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="平台" prop="platform">
          <el-select v-model="form.platform" clearable placeholder="请选择" class="filter-item" style="width: 300px">
            <el-option v-for="item in dict.platform_status" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" prop="owner">
          <el-select
            v-model="form.owner"
            filterable
            clearable
            style="width: 300px"
            placeholder="请选择负责人"
            @focus="loadUsers"
          >
            <el-option
              v-for="item in users"
              :key="item.id"
              :label="item.username"
              :value="item.username"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :data="crud.data" style="width: 100%" @selection-change="crud.selectionChangeHandler">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="店铺名称" />
      <el-table-column prop="location" label="站点" />
      <el-table-column prop="platform" label="平台" />
      <el-table-column prop="owner" label="负责人" />
      <el-table-column v-if="checkPer(['admin','serverDeploy:edit','serverDeploy:del'])" label="操作" width="150px" align="center">
        <template slot-scope="scope">
          <udOperation
            :data="scope.row"
            :permission="permission"
          />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>

import shop from '@/api/system/shop'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, name: null, location: null, platform: null, owner: null, enabled: true }
export default {
  name: 'Shop',
  components: { pagination, crudOperation, rrOperation, udOperation },
  cruds() {
    return CRUD({ title: '店铺', url: 'api/shop', crudMethod: { ...shop }, optShow: {
      add: true,
      edit: false,
      del: true,
      download: false,
      reset: true
    }})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['platform_status', 'site_status'],
  data() {
    return {
      users: [], // 存储负责人列表
      permission: {
        add: ['admin', 'serverDeploy:add'],
        edit: ['admin', 'serverDeploy:edit'],
        del: ['admin', 'serverDeploy:del']
      },
      rules: {
        name: [
          { required: true, message: '请输入店铺名称', trigger: 'blur' }
        ],
        location: [
          { required: true, message: '请输入站点', trigger: 'blur' }
        ],
        platform: [
          { required: true, message: '请输入平台', trigger: 'blur' }
        ],
        owner: [
          { required: true, message: '请选择负责人', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    // 加载负责人列表
    loadUsers() {
      // 如果已经加载过用户列表，则不再重复加载
      if (this.users.length > 0) {
        return
      }
      // 调用API获取用户列表，使用指定的参数
      shop.getDepts({
        page: 0,
        size: 10,
        sort: 'id,desc'
      }).then(res => {
        this.users = res.content
      }).catch(err => {
        console.error('获取负责人列表失败:', err)
      })
    },
    // 在表单打开时加载用户列表
    [CRUD.HOOK.afterToCU]() {
      this.loadUsers()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
