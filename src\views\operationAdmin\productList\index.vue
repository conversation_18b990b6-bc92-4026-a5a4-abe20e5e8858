<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.msku"
          clearable
          placeholder="MSKU"
          style="width: 200px"
          class="filter-item"
          @keyup.enter.native="toQuery"
        />
        <el-input
          v-model="query.asin"
          clearable
          placeholder="ASIN"
          style="width: 200px"
          class="filter-item"
          @keyup.enter.native="toQuery"
        />
        <el-select v-model="query.sid" placeholder="店铺" clearable filterable style="width: 200px" class="filter-item">
          <el-option v-for="item in productCategories" :key="item.sid" :label="item.name" :value="item.sid" />
        </el-select>
        <el-input
          v-model="query.localName"
          clearable
          placeholder="品名"
          style="width: 200px"
          class="filter-item"
          @keyup.enter.native="toQuery"
        />
        <el-button class="filter-item" size="mini" type="success" icon="el-icon-search" @click="toQuery">搜索</el-button>
        <el-button
          class="filter-item"
          size="mini"
          type="primary"
          icon="el-icon-refresh"
          @click="resetQuery"
        >重置</el-button>
      </div>
      <!--表格渲染-->
      <el-table
        ref="table"
        v-loading="loading"
        :data="data.list"
        style="width: 100%"
        @selection-change="selectionChangeHandler"
      >
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column prop="imageUrl" label="图片">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.imageUrl || defaultProductImage"
              :preview-src-list="[scope.row.imageUrl || defaultProductImage]"
              fit="contain"
              lazy
              class="el-avatar"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline" />
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="country" label="国家">
          <template slot-scope="scope">
            {{ scope.row.country || '暂无数据' }}
          </template>
        </el-table-column>
        <el-table-column prop="sellerName" label="店铺">
          <template slot-scope="scope">
            {{ scope.row.sellerName || '暂无数据' }}
          </template>
        </el-table-column>
        <el-table-column prop="asin" label="ASIN">
          <template slot-scope="scope">
            {{ scope.row.asin || '暂无数据' }}
          </template>
        </el-table-column>
        <el-table-column prop="localName" label="品名/SKU" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.localName || '暂无数据' }}
          </template>
        </el-table-column>
        <el-table-column prop="sellerSku" label="MSKU">
          <template slot-scope="scope">
            {{ scope.row.sellerSku || '暂无数据' }}
          </template>
        </el-table-column>

        <el-table-column prop="itemName" label="标题" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.itemName || '暂无数据' }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="goToObjectiveDetail(scope.row)">目标详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <el-pagination
        :page-size="page.size"
        :total="page.total"
        :current-page="page.page"
        style="margin-top: 8px;"
        layout="total, prev, pager, next, sizes"
        @size-change="sizeChangeHandler"
        @current-change="pageChangeHandler"
      />
    </div>

  </div>
</template>

<script>

import { shopList, getProductList } from '@/api/operationAdmin/productList'

export default {
  name: 'ProductList',
  components: {},
  data() {
    return {
      loading: false,
      searchToggle: true,
      defaultProductImage: require('@/assets/images/default-product.svg'), // 默认产品图片
      data: {
        list: []
      },
      page: {
        page: 1,
        size: 10,
        total: 0
      },
      query: {
        msku: '',
        asin: '',
        sid: '',
        localName: ''
      },
      productCategories: [],
      selections: [],
      permission: {
        add: ['admin', 'productList:add'],
        edit: ['admin', 'productList:edit'],
        del: ['admin', 'productList:del']
      }
    }
  },
  created() {
    this.loadProductCategories()
    this.init()
  },
  methods: {
    // 初始化数据
    init() {
      this.loading = true
      const params = {
        page: this.page.page,
        size: this.page.size,
        msku: this.query.msku,
        asin: this.query.asin,
        sid: this.query.sid,
        localName: this.query.localName
      }
      getProductList(params).then(res => {
        if (res.success === true) {
          this.data.list = res.data || []
          this.page.total = res.totalElements || 0
          this.page.page = res.currentPage || 1
          this.page.size = res.pageSize || 10
        } else {
          this.data.list = []
          this.page.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 当前页改变
    pageChangeHandler(e) {
      this.page.page = e
      this.refresh()
    },
    // 每页条数改变
    sizeChangeHandler(e) {
      this.page.size = e
      this.page.page = 1
      this.refresh()
    },
    // 刷新
    refresh() {
      this.init()
    },
    // 搜索
    toQuery() {
      this.page.page = 1
      this.refresh()
    },
    // 重置查询条件
    resetQuery() {
      this.query = {
        msku: '',
        asin: '',
        sid: '',
        localName: ''
      }
      this.toQuery()
    },
    // 加载产品分类
    loadProductCategories() {
      shopList().then(res => {
        if (res) {
          this.productCategories = res
        }
      }).catch(error => {
        console.error('获取产品分类失败:', error)
        this.productCategories = []
      })
    },
    // 切换搜索显示
    toggleSearch() {
      this.searchToggle = !this.searchToggle
    },
    // 选择改变
    selectionChangeHandler(val) {
      this.selections = val
    },
    // 跳转到目标详情页面
    goToObjectiveDetail(row) {
      // 通过路由跳转到目标详情页面，携带产品信息
      this.$router.push({
        path: '/operationAdmin/objective',
        query: {
          productInfo: encodeURIComponent(JSON.stringify(row))
        }
      })
    }

  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

// 图片样式
.el-avatar {
  width: 60px;
  height: 60px;
  border-radius: 4px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style>
