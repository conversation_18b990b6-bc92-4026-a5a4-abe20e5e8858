import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/exchange-rate',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/exchange-rate',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/exchange-rate',
    method: 'put',
    data
  })
}

// 获取退货报告数据
export function getReturnReportData(params) {
  return request({
    url: 'api/lingxing/refund-orders/by-asin',
    method: 'get',
    params
  })
}

// 导出退货报告数据
export function exportReturnReport(params) {
  return request({
    url: 'api/lingxing/refund-orders/by-asin/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取产品分类列表
export function getProductCategories(params) {
  return request({
    url: '/api/lingxing/reviews-summary',
    method: 'get',
    params
  })
}
// 英文名映射
export function getExcelFieldMapping(params) {
  return request({
    url: '/api/lingxing/refund-orders/excel-field-mapping',
    method: 'get',
    params
  })
}
export default { add, edit, del, getReturnReportData, exportReturnReport, getProductCategories, getExcelFieldMapping }
