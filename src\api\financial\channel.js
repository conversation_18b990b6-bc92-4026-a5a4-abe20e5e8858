import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/channel-order-tasks',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: `api/channel-order-tasks/${ids}`,
    method: 'delete'
  })
}

export function edit(data) {
  return request({
    url: 'api/channel-order-tasks',
    method: 'put',
    data
  })
}

export default { add, edit, del }
