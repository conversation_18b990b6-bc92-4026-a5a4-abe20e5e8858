<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input
          v-model="query.country"
          clearable
          placeholder="输入国家名称搜索"
          style="width: 200px"
          class="filter-item"
          @keyup.enter.native="crud.toQuery"
        />
        <rrOperation />
      </div>
      <crudOperation :permission="permission" />
    </div>
    <!--表单组件-->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :before-close="crud.cancelCU"
      :visible.sync="crud.status.cu > 0"
      :title="crud.status.title"
      width="470px"
    >
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="85px">
        <el-form-item label="国家" prop="country">
          <el-select v-model="form.country" clearable placeholder="请选择" class="filter-item" style="width: 300px">
            <el-option v-for="item in dict.country_status" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="汇率" prop="rate">
          <el-input v-model="form.rate" style="width: 300px" />
        </el-form-item>
        <el-form-item label="月份" prop="month">
          <el-date-picker
            v-model="form.month"
            type="month"
            value-format="yyyy-MM"
            style="width: 300px"
            placeholder="选择月份"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="crud.loading"
      :data="crud.data"
      style="width: 100%"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="country" label="国家" />
      <el-table-column prop="rate" label="汇率" />
      <el-table-column prop="month" label="月份" />
      <el-table-column
        v-if="checkPer(['admin', 'serverDeploy:edit', 'serverDeploy:del'])"
        label="操作"
        width="150px"
        align="center"
      >
        <template slot-scope="scope">
          <udOperation :data="scope.row" :permission="permission" />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>

import exchangeRate from '@/api/system/exchangeRate'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, country: null, rate: null, month: null }
export default {
  name: 'Shop',
  components: { pagination, crudOperation, rrOperation, udOperation },
  cruds() {
    return CRUD({
      title: '汇率', url: 'api/exchange-rate', crudMethod: { ...exchangeRate }, optShow: {
        add: true,
        edit: false,
        del: true,
        download: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['country_status'],
  data() {
    return {
      users: [], // 存储负责人列表
      permission: {
        add: ['admin', 'serverDeploy:add'],
        edit: ['admin', 'serverDeploy:edit'],
        del: ['admin', 'serverDeploy:del']
      },
      rules: {
        country: [
          { required: true, message: '请输入国家', trigger: 'blur' }
        ],
        rate: [
          { required: true, message: '请输入汇率', trigger: 'blur' }
        ],
        month: [
          { required: true, message: '请输入月份', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 在表单打开时加载用户列表
    [CRUD.HOOK.afterToCU]() {
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
